import 'package:flutter/material.dart';
import 'package:octasync_client/components/selector/employee_selector/index.dart';
import 'package:octasync_client/models/employee/employee_model.dart';
import 'package:octasync_client/imports.dart';

/// 员工选择器示例页面
class EmployeeSelectorExamplePage extends StatefulWidget {
  const EmployeeSelectorExamplePage({super.key});

  @override
  State<EmployeeSelectorExamplePage> createState() => _EmployeeSelectorExamplePageState();
}

class _EmployeeSelectorExamplePageState extends State<EmployeeSelectorExamplePage> {
  List<EmployeeModel> _selectedEmployees = [];
  List<String> _defaultSelectedIds = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('员工选择器示例')),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('员工选择器组件示例', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),

            // 基础员工选择器
            const Text('基础员工选择器:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            const SizedBox(height: 10),
            EmployeeSelector(
              onChange: (employees) {
                setState(() {
                  _selectedEmployees = employees;
                });
                print('选中的员工: ${employees.map((e) => e.name).join(', ')}');
              },
            ),
            const SizedBox(height: 20),

            // 带默认选中的员工选择器
            const Text('带默认选中的员工选择器:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            const SizedBox(height: 10),
            EmployeeSelector(
              defaultCheckedEmployeeIds: const ['2001', '2003'], // 张总和王小明
              onChange: (employees) {
                print('带默认选中的员工选择器 - 选中的员工: ${employees.map((e) => e.name).join(', ')}');
              },
            ),
            const SizedBox(height: 30),

            // 显示当前选中的员工信息
            const Text('当前选中的员工:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            const SizedBox(height: 10),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child:
                  _selectedEmployees.isEmpty
                      ? const Text('暂无选中的员工')
                      : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children:
                            _selectedEmployees.map((employee) {
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Row(
                                  children: [
                                    const Icon(Icons.person, size: 16, color: Colors.blue),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        '${employee.name} (${employee.parentName})',
                                        style: const TextStyle(fontSize: 14),
                                      ),
                                    ),
                                    Text(
                                      'ID: ${employee.id}',
                                      style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                      ),
            ),
            const SizedBox(height: 20),

            // 操作按钮
            Row(
              children: [
                AppButton(
                  text: '清空选择',
                  type: ButtonType.default_,
                  onPressed: () {
                    setState(() {
                      _selectedEmployees.clear();
                    });
                  },
                ),
                const SizedBox(width: 16),
                AppButton(
                  text: '设置默认选中',
                  type: ButtonType.primary,
                  onPressed: () {
                    setState(() {
                      _defaultSelectedIds = ['2005', '2006']; // 刘大伟和赵小丽
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),

            // 功能说明
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('功能说明:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                  SizedBox(height: 8),
                  Text('• 支持多级树形结构显示公司-部门-员工'),
                  Text('• 初始化时自动展开最顶级部门节点'),
                  Text('• 级联选择：选择部门会自动选择所有子部门和员工'),
                  Text('• 半选状态：部分子项被选中时显示半选状态（横线）'),
                  Text('• 支持单独选择/取消选择员工'),
                  Text('• 支持搜索功能'),
                  Text('• 支持拖拽排序已选员工'),
                  Text('• 支持默认选中员工'),
                  Text('• 右侧显示已选员工列表，可以单独移除'),
                  SizedBox(height: 8),
                  Text(
                    '测试建议：尝试选择"技术部"查看级联选择效果',
                    style: TextStyle(fontWeight: FontWeight.w600, color: Colors.blue),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
