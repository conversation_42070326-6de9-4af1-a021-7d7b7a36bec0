import 'package:json_annotation/json_annotation.dart';

part 'employee_model.g.dart';

//TODO:这是一个简单的demo模型后期根据接口修改
@JsonSerializable()
class EmployeeModel {
  @Json<PERSON>ey(name: 'Id')
  String? id;

  @Json<PERSON>ey(name: 'Name', defaultValue: '')
  String name;

  @Json<PERSON><PERSON>(name: 'ParentId')
  String? parentId;

  @JsonKey(name: 'ParentName', defaultValue: '')
  String parentName;

  @JsonKey(name: 'Type', defaultValue: 1)
  int type; // 1: 部门, 2: 员工

  EmployeeModel({this.id, this.name = '', this.parentId, this.parentName = '', this.type = 1});

  factory EmployeeModel.fromJson(Map<String, dynamic> json) {
    return _$EmployeeModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$EmployeeModelToJson(this);

  /// 检查是否为部门
  bool get isDepartment => type == 1;

  /// 检查是否为员工
  bool get isEmployee => type == 2;

  /// 获取显示名称
  String get displayName => name;

  /// 获取完整路径名称（包含父级名称）
  String get fullName {
    if (parentName.isNotEmpty) {
      return '$parentName - $name';
    }
    return name;
  }
}
