[{"Id": "1001", "Name": "总经理办公室", "ParentId": null, "ParentName": "", "Type": 1}, {"Id": "1002", "Name": "技术部", "ParentId": null, "ParentName": "", "Type": 1}, {"Id": "1003", "Name": "人力资源部", "ParentId": null, "ParentName": "", "Type": 1}, {"Id": "1004", "Name": "财务部", "ParentId": null, "ParentName": "", "Type": 1}, {"Id": "1005", "Name": "前端开发组", "ParentId": "1002", "ParentName": "技术部", "Type": 1}, {"Id": "1006", "Name": "后端开发组", "ParentId": "1002", "ParentName": "技术部", "Type": 1}, {"Id": "1007", "Name": "测试组", "ParentId": "1002", "ParentName": "技术部", "Type": 1}, {"Id": "1008", "Name": "招聘组", "ParentId": "1003", "ParentName": "人力资源部", "Type": 1}, {"Id": "1009", "Name": "薪酬福利组", "ParentId": "1003", "ParentName": "人力资源部", "Type": 1}, {"Id": "1010", "Name": "会计组", "ParentId": "1004", "ParentName": "财务部", "Type": 1}, {"Id": "2001", "Name": "张总", "ParentId": "1001", "ParentName": "总经理办公室", "Type": 2}, {"Id": "2002", "Name": "李秘书", "ParentId": "1001", "ParentName": "总经理办公室", "Type": 2}, {"Id": "2003", "Name": "王小明", "ParentId": "1005", "ParentName": "前端开发组", "Type": 2}, {"Id": "2004", "Name": "陈小红", "ParentId": "1005", "ParentName": "前端开发组", "Type": 2}, {"Id": "2005", "Name": "刘大伟", "ParentId": "1006", "ParentName": "后端开发组", "Type": 2}, {"Id": "2006", "Name": "赵小丽", "ParentId": "1006", "ParentName": "后端开发组", "Type": 2}, {"Id": "2007", "Name": "孙测试", "ParentId": "1007", "ParentName": "测试组", "Type": 2}, {"Id": "2008", "Name": "周小华", "ParentId": "1008", "ParentName": "招聘组", "Type": 2}, {"Id": "2009", "Name": "吴小芳", "ParentId": "1009", "ParentName": "薪酬福利组", "Type": 2}, {"Id": "2010", "Name": "郑会计", "ParentId": "1010", "ParentName": "会计组", "Type": 2}]