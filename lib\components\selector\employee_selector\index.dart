import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_selector_provider.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/employee/employee_model.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_selector_dialog.dart';

/// 员工选择器组件
class EmployeeSelector extends StatefulWidget {
  /// 提交按钮点击回调
  final void Function(List<EmployeeModel>)? onChange;

  /// 默认选中的员工ID列表(打开弹窗时默认选中)(适用于回显)
  final List<String>? defaultCheckedEmployeeIds;

  const EmployeeSelector({super.key, this.onChange, this.defaultCheckedEmployeeIds = const []});

  @override
  State<EmployeeSelector> createState() => _EmployeeSelectorState();
}

class _EmployeeSelectorState extends State<EmployeeSelector> {
  // 创建 GlobalKey 用于访问 EmployeeTree 的方法
  final GlobalKey<EmployeeTreeState> _employeeSelectorKey = GlobalKey<EmployeeTreeState>();

  // 维护当前选中的员工列表
  List<EmployeeModel> _selectedEmployees = [];

  @override
  void initState() {
    super.initState();
    // 如果有默认选中的员工ID，这里可以根据需要进行初始化
    // 但由于我们需要从JSON获取完整的员工信息，所以暂时保持为空
    // 实际的初始化会在弹窗确认后进行
  }

  /// 打开选择员工弹窗
  void _showEmployeeSelectorDialog(BuildContext context) {
    late EmployeeSelectorProvider provider;

    AppDialog.show(
      width: 800,
      height: 600,
      padding: EdgeInsetsGeometry.zero,
      context: context,
      title: '选择员工',
      isDrawer: false,
      child: ChangeNotifierProvider(
        create: (_) {
          provider = EmployeeSelectorProvider();
          // 设置默认选中的员工ID
          if (widget.defaultCheckedEmployeeIds != null &&
              widget.defaultCheckedEmployeeIds!.isNotEmpty) {
            provider.setDefaultCheckedEmployeeIds(widget.defaultCheckedEmployeeIds!);
          }
          return provider;
        },
        child: EmployeeSelectorDialog(employeeSelectorKey: _employeeSelectorKey),
      ),
      onConfirm: () {
        // 更新本地选中状态
        setState(() {
          _selectedEmployees = List.from(provider.checkedEmployees);
        });
        widget.onChange?.call(provider.checkedEmployees);
        context.pop();
      },
    );
  }

  /// 计算员工选择器的动态高度
  double _calculateSelectorHeight({
    required List<EmployeeModel> selectedEmployees, // 已选择的员工列表
    required int maxVisibleRows, // 最多可见行数，超过此数量将显示滚动条
    required double itemHeight, // 每个员工项的高度
    required double selectorPadding, // 选择器容器的内边距
    required double itemBottomMargin, // 每个员工项的底部外边距
  }) {
    // 基础高度：单项高度 + 上下内边距
    double baseHeight = itemHeight + selectorPadding * 2;

    // 如果没有选择员工或只选择了1个员工，返回基础高度
    if (selectedEmployees.length <= 1) {
      return baseHeight;
    }

    // 计算实际显示的行数（不超过最大可见行数）
    int actualVisibleRows =
        selectedEmployees.length > maxVisibleRows ? maxVisibleRows : selectedEmployees.length;

    // 计算多行情况下的总高度
    // 公式：单项高度 * 行数 + 上下内边距 * 2 + 每行底部外边距 * 行数
    return itemHeight * actualVisibleRows +
        selectorPadding * 2 +
        itemBottomMargin * actualVisibleRows;
  }

  /// 构建选择器
  @override
  Widget build(BuildContext context) {
    /// 最多可见行数
    int maxVisibleRows = 3;

    /// 选择器内边距
    double selectorPadding = 4;

    /// 每项的高度
    double itemHeight = 32;

    /// 每项的底部外边距
    double itemBottomMargin = 5;

    /// 使用提取的方法计算选择器动态高度
    double selectorHeight = _calculateSelectorHeight(
      selectedEmployees: _selectedEmployees,
      maxVisibleRows: maxVisibleRows,
      itemHeight: itemHeight,
      selectorPadding: selectorPadding,
      itemBottomMargin: itemBottomMargin,
    );

    return Container(
      width: double.infinity,
      height: selectorHeight,
      padding: EdgeInsets.all(selectorPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
        border: Border.all(color: context.border300),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          /// 已选员工列表
          Expanded(
            child:
                _selectedEmployees.isEmpty
                    ? const SizedBox()
                    : ReorderableListView(
                      shrinkWrap: true,
                      buildDefaultDragHandles: false,
                      children:
                          _selectedEmployees.asMap().entries.map((entry) {
                            final index = entry.key;
                            final employee = entry.value;
                            return Container(
                              key: ValueKey(employee.id),
                              padding: const EdgeInsets.all(5),
                              margin: EdgeInsets.only(right: 10, bottom: itemBottomMargin),
                              decoration: BoxDecoration(
                                color: context.background200,
                                borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  ReorderableDragStartListener(
                                    index: index,
                                    child: MouseRegion(
                                      cursor: SystemMouseCursors.click,
                                      child: Icon(
                                        IconFont.xianxing_tuodongpaixu,
                                        color: AppColors.icon200,
                                        size: AppIconSize.small,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Padding(
                                      padding: const EdgeInsets.only(bottom: 2, left: 8),
                                      child: Text(employee.name, overflow: TextOverflow.ellipsis),
                                    ),
                                  ),
                                  MouseRegion(
                                    cursor: SystemMouseCursors.click,
                                    child: GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          _selectedEmployees.removeWhere(
                                            (e) => e.id == employee.id,
                                          );
                                          widget.onChange?.call(_selectedEmployees);
                                        });
                                      },
                                      child: Icon(
                                        IconFont.mianxing_guanbi,
                                        color: context.icon200,
                                        size: AppIconSize.small,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                      onReorder: (oldIndex, newIndex) {
                        setState(() {
                          if (oldIndex < newIndex) {
                            newIndex -= 1;
                          }
                          final employee = _selectedEmployees.removeAt(oldIndex);
                          _selectedEmployees.insert(newIndex, employee);
                          widget.onChange?.call(_selectedEmployees);
                        });
                      },
                    ),
          ),

          /// 添加按钮
          AppButton(
            type: ButtonType.default_,
            size: ButtonSize.small,
            iconData: IconFont.xianxing_tianjia,
            color: AppColors.icon200,
            onPressed: () => _showEmployeeSelectorDialog(context),
          ),
        ],
      ),
    );
  }
}
