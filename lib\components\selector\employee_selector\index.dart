import 'package:flutter/material.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_selector_dialog.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_selector_provider.dart';
import 'package:provider/provider.dart';

class EmployeeSelector extends StatefulWidget {
  /// 数据改变回调
  final void Function(List<dynamic>)? onChange;

  /// 默认选中的人员ID列表(打开弹窗时默认选中)(适用于回显)
  final List<String>? defaultCheckedEmployeeIds;

  const EmployeeSelector({super.key, this.onChange, this.defaultCheckedEmployeeIds = const []});

  @override
  State<EmployeeSelector> createState() => _EmployeeSelectorState();
}

class _EmployeeSelectorState extends State<EmployeeSelector> {
  // 维护当前选中的人员列表
  List<dynamic> _selectedEmployees = [];

  /// 打开选择人员弹窗
  void _showEmployeeSelectorDialog(BuildContext context) {
    late EmployeeSelectorProvider provider;

    AppDialog.show(
      width: 800,
      height: 600,
      padding: EdgeInsetsGeometry.zero,
      context: context,
      title: '选择人员',
      isDrawer: false,
      child: ChangeNotifierProvider(
        create: (_) {
          provider = EmployeeSelectorProvider();
          // 设置默认选中的部门ID
          if (widget.defaultCheckedEmployeeIds != null &&
              widget.defaultCheckedEmployeeIds!.isNotEmpty) {
            provider.setDefaultCheckedEmployeeIds(widget.defaultCheckedEmployeeIds!);
          }
          return provider;
        },
        child: EmployeeSelectorDialog(),
      ),
      onConfirm: () {
        // 更新本地选中状态
        setState(() {
          _selectedEmployees = List.from(provider.checkedEmployees);
        });
        widget.onChange?.call(provider.checkedEmployees);
        context.pop();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppButton(
      text: '人员按钮',
      type: ButtonType.primary,
      onPressed: () => _showEmployeeSelectorDialog(context),
    );
  }
}
