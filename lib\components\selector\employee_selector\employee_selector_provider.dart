import 'package:flutter/material.dart';
import 'package:octasync_client/models/employee/employee_model.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree.dart';

/// 员工选择器状态管理 Provider
class EmployeeSelectorProvider extends ChangeNotifier {
  /// 当前选中的员工列表
  List<EmployeeModel> _checkedEmployees = [];

  /// 搜索关键词
  String _searchQuery = '';

  /// 默认选中的员工ID列表
  List<String> _defaultCheckedEmployeeIds = [];

  /// 获取当前选中的员工列表
  List<EmployeeModel> get checkedEmployees => List.unmodifiable(_checkedEmployees);

  /// 获取当前搜索关键词
  String get searchQuery => _searchQuery;

  /// 获取选中员工数量
  int get checkedCount => _checkedEmployees.length;

  /// 获取默认选中的员工ID列表
  List<String> get defaultCheckedEmployeeIds => List.unmodifiable(_defaultCheckedEmployeeIds);

  /// 设置默认选中的员工ID列表
  void setDefaultCheckedEmployeeIds(List<String> employeeIds) {
    _defaultCheckedEmployeeIds = List.from(employeeIds);
    notifyListeners();
  }

  /// 检查员工ID是否在默认选中列表中
  bool isDefaultChecked(String employeeId) {
    return _defaultCheckedEmployeeIds.contains(employeeId);
  }

  /// 更新选中的员工列表
  void updateCheckedEmployees(List<EmployeeModel> employees) {
    _checkedEmployees = List.from(employees);
    notifyListeners();
  }

  /// 添加选中的员工
  void addCheckedEmployee(EmployeeModel employee) {
    if (!_checkedEmployees.any((e) => e.id == employee.id)) {
      _checkedEmployees.add(employee);
      notifyListeners();
    }
  }

  /// 移除选中的员工
  void removeCheckedEmployee(String employeeId) {
    _checkedEmployees.removeWhere((e) => e.id == employeeId);
    notifyListeners();
  }

  /// 清空所有选中的员工
  void clearAllCheckedEmployees() {
    _checkedEmployees.clear();
    notifyListeners();
  }

  /// 设置搜索关键词
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  /// 重置状态
  void reset() {
    _checkedEmployees.clear();
    _searchQuery = '';
    notifyListeners();
  }

  /// 重置到默认选中状态（保留默认选中的员工）
  void resetToDefault() {
    _checkedEmployees.clear();
    _searchQuery = '';
    // 注意：这里不清空 _defaultCheckedEmployeeIds，因为它们应该保持
    notifyListeners();
  }

  /// 应用默认选中状态到员工树（数据加载完成后调用）
  void applyDefaultCheckedState(GlobalKey<EmployeeTreeState> treeKey) {
    if (_defaultCheckedEmployeeIds.isNotEmpty) {
      final treeState = treeKey.currentState;
      if (treeState != null) {
        // 先重置所有选中状态
        treeState.resetAllNodesCheck();
        // 然后设置默认选中的员工
        for (final employeeId in _defaultCheckedEmployeeIds) {
          treeState.checkNode(employeeId);
        }
        // 更新Provider中的选中列表
        final checkedEmployees = treeState.getAllCheckedEmployees();
        updateCheckedEmployees(checkedEmployees);
      }
    }
  }
}
